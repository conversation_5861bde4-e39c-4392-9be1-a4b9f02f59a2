{"$schema": "https://www.speedscope.app/file-format-schema.json", "profiles": [{"type": "sampled", "name": "Thread 0x203322200 \"MainThread\"", "unit": "seconds", "startValue": 0.0, "endValue": 22.6, "samples": [[12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 9, 5, 4, 8, 7, 0, 6, 5, 4, 3, 2, 1, 0], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 23, 5, 4, 8, 7, 0, 22, 20, 0, 5, 4, 8, 7, 0, 21, 20, 0, 5, 4, 8, 7, 0, 19, 5, 4, 8, 7, 0, 18, 17, 16, 15, 14, 13], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 23, 5, 4, 8, 7, 0, 22, 20, 0, 5, 4, 8, 7, 0, 21, 20, 0, 5, 4, 8, 7, 0, 19, 5, 4, 8, 7, 0, 18, 17, 16, 15, 28, 27, 26], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 23, 5, 4, 8, 7, 0, 22, 20, 0, 5, 4, 8, 7, 0, 30, 20, 0, 5, 4, 8, 7, 0, 29], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 45, 5, 4, 8, 7, 0, 44, 5, 4, 8, 7, 0, 43, 5, 10, 0, 5, 4, 8, 7, 0, 42, 5, 4, 8, 7, 0, 41, 5, 10, 0, 5, 4, 8, 7, 0, 40, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 10, 0, 5, 4, 8, 7, 0, 39, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 37, 17, 16, 36, 35, 34, 33, 32, 31], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 45, 5, 4, 8, 7, 0, 48, 5, 4, 8, 7, 0, 47, 5, 4, 8, 7, 0, 46, 5, 4, 3, 2, 1, 0], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 45, 5, 4, 8, 7, 0, 48, 5, 4, 8, 7, 0, 47, 5, 4, 8, 7, 0, 46, 5, 4, 3, 2, 1, 0], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 59, 5, 4, 8, 7, 0, 58, 5, 4, 8, 7, 0, 57, 5, 4, 8, 7, 0, 56, 5, 4, 8, 7, 0, 55, 5, 4, 8, 7, 0, 54, 20, 0, 5, 4, 8, 7, 0, 53, 5, 4, 8, 7, 0, 52, 5, 4, 8, 7, 0, 51, 50, 49], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 59, 5, 4, 8, 7, 0, 58, 5, 4, 8, 7, 0, 57, 5, 4, 8, 7, 0, 56, 5, 4, 8, 7, 0, 55, 5, 4, 8, 7, 0, 54, 20, 0, 5, 4, 8, 7, 0, 53, 5, 4, 8, 7, 0, 52, 5, 4, 8, 7, 0, 51, 50, 49], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 65, 5, 4, 8, 7, 0, 64, 5, 4, 8, 7, 0, 63, 5, 4, 8, 62, 61, 60], [12, 5, 4, 8, 7, 0, 11, 5, 10, 0, 5, 4, 8, 7, 0, 25, 5, 4, 8, 7, 0, 24, 5, 4, 8, 7, 0, 65, 5, 4, 8, 7, 0, 70, 5, 4, 8, 7, 0, 69, 5, 4, 8, 7, 0, 68, 67, 66], [85, 5, 4, 8, 7, 0, 84, 5, 4, 8, 7, 0, 83, 5, 4, 8, 7, 0, 82, 5, 4, 8, 7, 0, 81, 5, 4, 8, 7, 0, 80, 5, 4, 8, 7, 0, 79, 5, 4, 8, 7, 0, 78, 5, 4, 8, 7, 0, 77, 20, 0, 5, 4, 8, 7, 0, 76, 75, 74, 73, 72, 71], [85, 5, 4, 8, 7, 0, 84, 5, 4, 8, 7, 0, 83, 5, 4, 8, 7, 0, 82, 5, 4, 8, 7, 0, 81, 5, 4, 8, 7, 0, 80, 5, 4, 8, 7, 0, 79, 5, 4, 8, 7, 0, 90, 5, 4, 8, 7, 0, 89, 88, 87, 86], [85, 5, 4, 8, 7, 0, 84, 5, 4, 8, 7, 0, 97, 96, 95, 94, 93, 92, 91], [85, 5, 4, 8, 7, 0, 104, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 103, 102, 101, 100, 99, 98], [85, 5, 4, 8, 7, 0, 104, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 123, 5, 4, 8, 7, 0, 122, 121, 120, 119, 118, 117, 116, 5, 4, 8, 7, 0, 115, 5, 4, 8, 7, 0, 114, 5, 10, 0, 5, 4, 8, 7, 0, 113, 5, 4, 8, 7, 0, 112, 5, 4, 8, 7, 0, 111, 5, 110, 109, 108, 107, 106, 105], [85, 5, 4, 8, 7, 0, 104, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 123, 5, 4, 8, 7, 0, 122, 121, 120, 119, 118, 117, 116, 5, 4, 8, 7, 0, 115, 5, 4, 8, 7, 0, 114, 5, 10, 0, 5, 4, 8, 7, 0, 113, 5, 4, 8, 7, 0, 129, 5, 4, 8, 7, 0, 128, 20, 0, 5, 4, 8, 7, 0, 127, 20, 0, 5, 4, 8, 7, 0, 126, 20, 0, 125, 124], [85, 5, 4, 8, 7, 0, 104, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 123, 5, 4, 8, 7, 0, 122, 121, 120, 119, 118, 117, 116, 5, 4, 8, 7, 0, 115, 5, 4, 8, 7, 0, 114, 5, 10, 0, 5, 4, 8, 7, 0, 138, 5, 4, 8, 7, 0, 137, 5, 4, 8, 7, 0, 136, 5, 4, 8, 7, 0, 135, 5, 4, 8, 7, 0, 134, 20, 0, 5, 4, 8, 7, 0, 133, 5, 4, 8, 7, 0, 132, 131, 130], [85, 5, 4, 8, 7, 0, 104, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 123, 5, 4, 8, 7, 0, 122, 121, 120, 119, 118, 117, 116, 5, 4, 8, 7, 0, 153, 5, 10, 0, 5, 10, 0, 5, 4, 8, 7, 0, 152, 20, 0, 5, 4, 8, 7, 0, 151, 5, 4, 8, 7, 0, 150, 5, 10, 0, 5, 4, 8, 7, 0, 149, 20, 0, 5, 4, 8, 7, 0, 148, 5, 4, 8, 7, 0, 147, 5, 4, 8, 7, 0, 146, 5, 4, 8, 7, 0, 145, 5, 4, 8, 7, 0, 144, 5, 4, 8, 7, 0, 143, 5, 10, 0, 5, 10, 0, 5, 4, 8, 7, 0, 142, 20, 0, 5, 4, 8, 7, 0, 141, 5, 4, 8, 7, 0, 140, 5, 4, 8, 7, 0, 139, 101, 100, 99, 98], [85, 5, 4, 8, 7, 0, 104, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 123, 5, 4, 8, 7, 0, 122, 121, 120, 119, 118, 117, 116, 5, 4, 8, 7, 0, 153, 5, 10, 0, 5, 10, 0, 5, 4, 8, 7, 0, 152, 20, 0, 5, 4, 8, 7, 0, 151, 5, 4, 8, 7, 0, 150, 5, 10, 0, 5, 4, 8, 7, 0, 149, 20, 0, 5, 4, 8, 7, 0, 148, 5, 4, 8, 7, 0, 147, 5, 4, 8, 7, 0, 146, 5, 4, 8, 7, 0, 172, 5, 4, 8, 7, 0, 171, 5, 4, 8, 7, 0, 170, 5, 4, 8, 7, 0, 169, 5, 4, 8, 7, 0, 168, 5, 4, 8, 7, 0, 167, 5, 4, 8, 7, 0, 166, 5, 4, 8, 7, 0, 165, 5, 10, 0, 5, 4, 8, 7, 0, 164, 5, 4, 8, 7, 0, 163, 5, 4, 8, 7, 0, 162, 5, 4, 8, 7, 0, 161, 5, 4, 8, 7, 0, 160, 5, 4, 8, 7, 0, 159, 101, 158, 157, 156, 155, 154], [180, 5, 4, 8, 7, 0, 179, 5, 4, 8, 7, 0, 178, 5, 4, 8, 7, 0, 177, 5, 4, 8, 7, 0, 176, 5, 4, 8, 7, 0, 175, 5, 4, 8, 7, 0, 174, 5, 4, 8, 7, 0, 173, 5, 4, 3, 2, 1, 0], [180, 5, 4, 8, 7, 0, 179, 5, 4, 8, 7, 0, 178, 5, 4, 8, 7, 0, 177, 5, 4, 8, 7, 0, 176, 5, 4, 8, 7, 0, 186, 185, 184, 183, 182, 181], [180, 5, 4, 8, 7, 0, 179, 5, 4, 8, 7, 0, 178, 5, 4, 8, 7, 0, 177, 5, 4, 8, 7, 0, 201, 5, 4, 8, 7, 0, 200, 20, 0, 5, 4, 8, 7, 0, 199, 198, 197, 196, 195, 194, 193, 192, 191, 190, 189, 188, 187], [214, 5, 4, 8, 7, 0, 213, 5, 4, 8, 7, 0, 212, 5, 4, 8, 7, 0, 211, 5, 10, 0, 5, 4, 8, 7, 0, 210, 5, 4, 8, 7, 0, 209, 5, 4, 8, 7, 0, 208, 5, 4, 8, 7, 0, 207, 5, 4, 8, 7, 0, 206, 5, 4, 8, 7, 0, 205, 5, 4, 8, 7, 0, 204, 5, 4, 8, 7, 0, 203, 5, 4, 8, 7, 0, 202, 5, 4, 8, 62, 61, 60], [214, 5, 4, 8, 7, 0, 213, 5, 4, 8, 7, 0, 212, 5, 4, 8, 7, 0, 224, 5, 4, 8, 7, 0, 223, 5, 10, 0, 5, 4, 8, 7, 0, 222, 5, 4, 8, 7, 0, 221, 5, 10, 0, 5, 4, 8, 7, 0, 220, 5, 4, 8, 38, 0, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 219, 5, 4, 8, 7, 0, 218, 5, 10, 0, 5, 4, 8, 7, 0, 217, 20, 0, 5, 4, 8, 7, 0, 216, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 215, 5, 4, 3, 2, 1, 0], [214, 5, 4, 8, 7, 0, 213, 5, 4, 8, 7, 0, 212, 5, 4, 8, 7, 0, 224, 5, 4, 8, 7, 0, 223, 5, 10, 0, 5, 4, 8, 7, 0, 222, 5, 4, 8, 7, 0, 221, 5, 10, 0, 5, 4, 8, 7, 0, 220, 5, 4, 8, 38, 0, 5, 4, 8, 7, 0, 225], [214, 5, 4, 8, 7, 0, 213, 5, 4, 8, 7, 0, 212, 5, 4, 8, 7, 0, 224, 5, 4, 8, 7, 0, 223, 5, 4, 8, 7, 0, 239, 5, 4, 8, 7, 0, 238, 5, 4, 8, 7, 0, 237, 5, 10, 0, 5, 4, 8, 7, 0, 236, 5, 4, 8, 7, 0, 235, 5, 4, 8, 7, 0, 234, 5, 4, 8, 7, 0, 233, 5, 10, 0, 5, 4, 8, 7, 0, 232, 5, 4, 8, 7, 0, 231, 5, 10, 0, 5, 4, 8, 7, 0, 230, 5, 4, 8, 7, 0, 229, 5, 4, 8, 7, 0, 228, 227, 226], [214, 5, 4, 8, 7, 0, 213, 5, 4, 8, 7, 0, 212, 5, 4, 8, 7, 0, 224, 5, 4, 8, 7, 0, 223, 5, 4, 8, 7, 0, 239, 5, 4, 8, 7, 0, 238, 5, 4, 8, 7, 0, 237, 5, 10, 0, 5, 4, 8, 7, 0, 236, 5, 4, 8, 7, 0, 235, 5, 4, 8, 7, 0, 234, 5, 4, 8, 7, 0, 233, 5, 10, 0, 5, 4, 8, 7, 0, 232, 5, 4, 8, 7, 0, 231, 5, 4, 8, 7, 0, 241, 5, 10, 0, 5, 10, 0, 5, 4, 8, 7, 0, 240, 5, 4, 8, 62, 61, 60], [262, 261, 260, 259, 258, 257, 256, 255, 254, 253, 252, 251, 250, 249, 246, 248, 247, 246, 245, 244, 243, 242], [262, 261, 260, 259, 258, 257, 256, 255, 267, 266, 265, 264, 263], [262, 261, 260, 259, 258, 257, 256, 255, 267, 269, 268], [262, 261, 260, 259, 258, 257, 256, 255, 267, 271, 270], [262, 261, 280, 279, 278, 277, 276, 275, 274, 273, 272], [262, 261, 280, 279, 278, 277, 276, 275, 274, 273, 272], [262, 261, 280, 279, 278, 277, 276, 275, 274, 273, 272], [262, 261, 280, 279, 278, 277, 276, 275, 274, 273, 272], [262, 261, 280, 279, 278, 277, 276, 275, 274, 273, 272], [262, 261, 280, 279, 278, 277, 276, 275, 274, 273, 272], [262, 261, 280, 279, 278, 277, 276, 275, 274, 273, 272], [262, 317, 258, 257, 256, 255, 316, 315, 314, 313, 312, 311, 310, 309], [262, 317, 258, 257, 256, 255, 316, 315, 314, 313, 312, 311, 310, 309], [262, 317, 258, 257, 256, 255, 341, 340, 339, 338, 117, 116, 5, 4, 8, 7, 0, 337, 5, 4, 8, 7, 0, 336, 5, 4, 8, 7, 0, 335, 5, 4, 8, 7, 0, 334, 5, 4, 8, 7, 0, 333, 5, 4, 8, 7, 0, 332, 5, 4, 8, 7, 0, 331, 5, 4, 8, 7, 0, 330, 5, 4, 8, 7, 0, 329, 5, 4, 8, 7, 0, 328, 5, 4, 8, 7, 0, 327, 5, 4, 8, 7, 0, 326, 5, 4, 8, 7, 0, 325, 5, 4, 8, 7, 0, 324, 5, 4, 8, 7, 0, 323, 5, 4, 8, 7, 0, 322, 5, 4, 8, 7, 0, 321, 320, 319, 318], [262, 317, 258, 257, 256, 255, 341, 342], [262, 317, 258, 257, 256, 255, 346, 345, 344, 343], [262, 317, 258, 257, 256, 255, 346, 345, 344, 343], [262, 317, 258, 257, 256, 255, 346, 345, 344, 343], [262, 317, 258, 257, 256, 255, 346, 345, 344, 343], [262, 317, 258, 257, 256, 255, 341, 342, 366, 365, 364, 363, 362, 361, 360, 359, 358, 357, 356, 355, 354, 353, 352, 351, 350, 349, 348, 347], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 369, 368, 367], [262, 317, 258, 257, 256, 255, 341, 342, 366, 379, 364, 378, 354, 377, 376, 375, 374, 373], [262, 317, 258, 257, 256, 255, 341, 342, 366, 365, 364, 363, 354, 377, 376, 375, 374, 373], [262, 317, 258, 257, 256, 255, 341, 342, 366, 365, 364, 363, 354, 377, 376, 375, 374, 373], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 380, 368, 367], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 383, 382, 381], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 256, 255, 341, 342, 392, 391, 390, 389, 388, 387, 386, 385, 385], [262, 317, 258, 257, 256, 255, 396, 395, 394, 393], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 401, 400, 399, 398, 397], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 401, 400, 399, 409, 408, 412, 411, 410, 411, 410, 409, 408, 407, 406, 405, 404, 403, 402], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 401, 400, 399, 409, 408, 412, 411, 410, 411, 410, 409, 408, 407, 406, 418, 417, 416, 415, 414, 413], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 383, 420, 419], [262, 317, 258, 257, 256, 255, 341, 439, 438, 437, 436, 435, 434, 433, 432, 431, 430, 429, 428, 427, 426, 425, 424, 423, 422, 421], [262, 317, 258, 257, 256, 255, 449, 371, 251, 250, 249, 246, 248, 247, 246, 401, 400, 399, 409, 408, 407, 406, 418, 448, 447, 446, 414, 445, 444, 443, 442, 441, 440], [262, 317, 258, 257, 256, 255, 449, 371, 251, 250, 249, 246, 248, 247, 246, 401, 400, 399, 409, 408, 407, 406, 418, 448, 447, 446, 414, 445, 444, 456, 455, 454, 404, 453, 452, 451, 450], [262, 317, 258, 257, 256, 255, 346, 345, 344, 343], [262, 317, 258, 257, 256, 255, 346, 345, 344, 343], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 458, 462, 461, 460, 459], [262, 317, 258, 257, 256, 255, 474, 473, 472, 471, 470, 364, 469, 362, 361, 468, 467, 466, 465, 464, 463], [262, 317, 258, 257, 256, 255, 474, 473, 472, 471, 470, 364, 469, 362, 361, 468, 467, 466, 465, 464, 481, 480, 479, 478, 477, 476, 475], [262, 317, 258, 257, 256, 255, 474], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 341, 490, 489, 488, 5, 4, 8, 7, 0, 487, 5, 4, 8, 7, 0, 486, 5, 4, 8, 7, 0, 485, 5, 4, 8, 7, 0, 484, 5, 4, 8, 7, 0, 483, 5, 4, 8, 7, 0, 482, 5, 4, 3, 2, 1, 0], [262, 317, 258, 257, 256, 255, 458, 462, 461, 460, 459], [262, 317, 258, 257, 256, 255, 458, 462, 461, 460, 459], [262, 317, 258, 257, 256, 255, 458, 462, 461, 460, 459], [262, 317, 258, 257, 256, 255, 458, 462, 461, 460, 459], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 401, 400, 399, 409, 408, 412, 411, 410, 411, 410, 409, 408, 407, 406, 418, 448, 447, 446, 414, 445, 444, 456, 455], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 384, 494, 493, 492, 491], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 244], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 499, 498, 497, 496, 495], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 499, 498, 407, 406, 418, 503, 502, 501, 500], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 504], [262, 317, 258, 257, 256, 255, 341, 505], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 494, 493, 492, 506], [262, 317, 258, 257, 256, 255, 510, 509, 508, 507, 460, 459], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 384, 513, 512, 511], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 499, 498, 515, 411, 410, 409, 408, 411, 410, 514], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 494, 493, 516, 446], [262, 317, 258, 257, 256, 255, 341, 525, 524, 523, 522, 521, 392, 391, 390, 389, 388, 520, 519, 518, 517], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527, 526, 447, 446, 414, 445, 444, 456, 455, 454, 404, 453, 452, 451, 450], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 401, 400, 399, 398, 533, 532, 531], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 536, 535, 493, 492, 534], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 504], [262, 317, 258, 257, 256, 255, 538, 537, 364, 363, 354, 377, 376, 375, 374, 373], [262, 317, 258, 257, 256, 255, 341, 505, 540, 539], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 244, 542, 541], [262, 317, 258, 257, 543], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 341, 549, 548, 547, 546, 545, 544], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 536, 535, 493, 492, 550], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 384, 383, 420, 551], [262, 317, 258, 257, 256, 255, 552, 366, 365, 364, 363, 354, 377, 376, 375, 374, 373], [262, 317, 258, 257, 256, 255, 552, 366, 365, 364, 363, 354, 377, 376, 375, 374, 373], [262, 317, 258, 257, 256, 255, 372, 371, 251, 555, 554, 553], [262, 317, 258, 257, 256, 255, 372, 371, 251, 555, 554, 560, 559, 558, 557, 556, 556], [262, 317, 258, 257, 256, 255, 565, 564, 563, 562, 561, 375, 374], [262, 317, 258, 257, 256, 255, 565, 564, 566], [262, 317, 258, 257, 256, 255, 341, 505], [262, 317, 258, 257, 567], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 458, 462, 461, 569, 568], [262, 317, 258, 257, 256, 255, 572, 571, 570], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 494, 493, 492, 573], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 384, 530, 529, 574], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 256, 255, 341, 549, 548, 547, 579, 578, 577, 576, 575], [262, 317, 258, 257, 256, 255, 341, 549, 548, 547, 579, 578, 577, 586, 585, 584, 583, 582, 581, 580], [262, 317, 258, 257, 256, 255, 341, 549, 548, 547, 579, 578, 577, 586, 585, 584, 583, 582, 590, 589, 588, 587], [262, 317, 258, 257, 256, 255, 572], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 543, 591], [262, 317, 258, 257, 256, 255, 592], [262, 317, 258, 257, 256, 255, 565, 564, 566, 598, 597, 596, 595, 594, 270, 593], [262, 317, 258, 257, 256, 255, 565, 564, 563, 599], [262, 317, 258, 257, 600], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527, 526, 447, 446, 414, 445, 444, 602, 601], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527, 526, 447, 446, 414, 445, 444, 602, 601], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 383, 382, 603], [262, 317, 258, 257, 604], [262, 317, 258, 257, 256, 255, 341, 607, 606, 605], [262, 317, 258, 257, 256, 255, 565, 564, 566, 598, 597, 612, 611, 610, 609, 608], [262, 317, 258, 257, 256, 255, 341, 505], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 401, 614, 613], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 341, 505, 617, 616, 615], [262, 317, 258, 257, 256, 255, 341, 505, 617, 616, 615], [262, 317, 258, 257, 256, 255, 341, 505, 617, 616, 618], [262, 317, 258, 257, 256, 255, 631, 630, 629, 628, 363, 362, 361, 627, 626, 625, 624, 623, 622, 621, 620, 619], [262, 317, 258, 257, 256, 255, 565, 564, 566, 598, 597, 596, 595, 594, 632], [262, 317, 258, 257, 256, 255, 341, 549, 643, 642, 641, 640, 639, 638, 637, 636, 635, 634, 633], [262, 317, 258, 257, 256, 255, 341, 549, 643, 642, 641, 640, 639, 638, 637, 636, 635, 634, 633], [262, 317, 258, 257, 256, 255, 341, 549, 643, 642, 641, 640, 639, 638, 637, 636, 635, 634, 633], [262, 317, 258, 257, 256, 255, 458, 462, 650, 649, 648, 647, 646, 645, 644, 571, 570], [262, 317, 258, 257, 256, 255, 572], [262, 317, 258, 257, 256, 255, 458, 462, 461, 569, 568], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 383, 382, 551], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 384, 494, 493, 492, 651], [262, 317, 258, 257, 256, 255, 458, 652], [262, 317, 258, 257, 654, 653], [262, 317, 258, 257, 654, 653], [262, 317, 258, 257, 543, 591], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 654], [262, 317, 258, 257, 256, 255, 341, 607, 656, 655], [262, 317, 258, 257, 657], [262, 317, 258, 257, 658], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 401, 400, 399, 409, 408, 412, 411, 410, 411, 410, 398, 659], [262, 317, 258, 257, 660], [262, 317, 258, 257, 661], [262, 317, 258, 257, 662], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 245, 246, 248, 246, 384, 536, 535, 493, 663], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 458, 462, 461, 664], [262, 317, 258, 257, 256, 255, 458, 462, 461, 569, 568], [262, 317, 258, 257, 256, 255, 458, 462, 461, 569], [262, 317, 258, 257, 256, 255, 565, 564, 563, 599], [262, 317, 258, 257, 256, 255, 565, 564, 563, 562, 561, 375, 665], [262, 317, 258, 257, 256, 255, 341, 505], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 369, 673], [262, 317, 258, 257, 256, 255, 341, 607], [262, 317, 258, 257, 256, 255, 341, 607, 674], [262, 317, 258, 257, 256, 255, 565, 564, 677, 676, 675], [262, 317, 258, 257, 256, 255, 341, 700, 699, 698, 641, 697, 696, 695, 694, 693, 692, 691, 690, 689, 688, 687, 686, 685, 684, 683, 682, 681, 680, 679, 678], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527, 526, 447, 446, 414, 445, 444, 602, 601], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527, 526, 447, 446, 414, 445, 444, 602, 601], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527, 526, 447, 446, 414, 445, 444, 602, 601], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527, 526, 447, 446, 414, 445, 444, 602, 601], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 246, 248, 384, 530, 529, 528, 527], [262, 317, 258, 257, 256, 255, 565, 564, 566, 598, 597, 612, 611, 610, 264, 702, 701], [262, 317, 258, 257, 256, 255, 341, 505, 271], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 703], [262, 317, 258, 257, 256, 255, 572], [262, 317, 258, 257, 256, 255, 341, 704], [262, 317, 258, 257, 707, 706, 705], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 499, 498, 412, 411, 410, 411, 410, 398, 708], [262, 317, 258, 257, 662], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 256, 255, 341, 700, 711, 547, 579, 578, 577, 586, 710, 709], [262, 317, 258, 257, 256, 255, 341, 700, 711, 547, 579, 578, 577, 586, 710, 713, 712], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 384, 513, 416, 415, 414, 445, 444, 715, 714], [262, 317, 258, 257, 256, 255, 565, 564, 566, 598, 716], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 256], [262, 317, 258, 257, 256, 255, 458, 457], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 401], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 703], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 369, 673], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 717], [262, 317, 258, 257, 256, 255, 565, 564, 677, 676, 719, 718], [262, 317, 258, 257, 256, 255, 565, 564, 563, 599], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 370, 720], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 721], [262, 317, 258, 257, 657], [262, 317, 258, 257, 256, 255, 572, 571, 570, 722], [262, 317, 258, 257, 256, 255], [262, 317, 258, 257, 657], [262, 317, 258, 257, 543, 591], [262, 317, 258, 257, 256, 723], [262, 317, 258, 257, 256, 255, 727, 726, 725, 724], [262, 317, 258, 257, 543], [262, 317, 258, 257, 256, 255, 572], [262, 317, 258, 257, 256, 255, 341, 505, 730, 729, 728], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 499, 498, 407, 406, 418, 417, 736, 735, 734, 733, 732, 731], [262, 317, 258, 257, 256, 255, 372, 371, 251, 250, 249, 246, 248, 247, 246, 401, 737], [], [], [], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30D1EF000 \"ThreadPoolExecutor-0_0\"", "unit": "seconds", "startValue": 0.0, "endValue": 13.0, "samples": [[291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 308, 307, 306, 305, 304, 303, 302, 301, 300, 299, 298, 297, 296, 295, 294, 293, 292], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272], [291, 290, 289, 288, 287, 286, 285, 284, 283, 282, 281, 277, 276, 275, 274, 273, 272]], "weights": [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30D272000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 2.3, "samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30D2F5000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 25.7, "samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30D3FB000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 29.5, "samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30D47E000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.4, "samples": [[], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30D584000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 28.3, "samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30D99C000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.1, "samples": [[]], "weights": [0.1]}, {"type": "sampled", "name": "Thread 0x30DBA8000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.1, "samples": [[]], "weights": [0.1]}, {"type": "sampled", "name": "Thread 0x30DDB4000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.3, "samples": [[], [], []], "weights": [0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30E043000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.4, "samples": [[], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30E149000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.4, "samples": [[], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30E1CC000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.1, "samples": [[]], "weights": [0.1]}, {"type": "sampled", "name": "Thread 0x30E24F000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.4, "samples": [[], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30E561000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.4, "samples": [[], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30E5E4000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.1, "samples": [[]], "weights": [0.1]}, {"type": "sampled", "name": "Thread 0x30E667000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.4, "samples": [[], [], [], []], "weights": [0.1, 0.1, 0.1, 0.1]}, {"type": "sampled", "name": "Thread 0x30E6EA000 \"\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.1, "samples": [[]], "weights": [0.1]}, {"type": "sampled", "name": "Thread 0x310461000 \"asyncio_1\"", "unit": "seconds", "startValue": 0.0, "endValue": 0.1, "samples": [[291, 290, 289, 288, 287, 672, 671, 670, 669, 668, 667, 666]], "weights": [0.1]}], "shared": {"frames": [{"name": "_call_with_frames_removed", "file": "<frozen importlib._bootstrap>", "line": 241, "col": null}, {"name": "create_module", "file": "<frozen importlib._bootstrap_external>", "line": 1176, "col": null}, {"name": "module_from_spec", "file": "<frozen importlib._bootstrap>", "line": 571, "col": null}, {"name": "_load_unlocked", "file": "<frozen importlib._bootstrap>", "line": 674, "col": null}, {"name": "_find_and_load_unlocked", "file": "<frozen importlib._bootstrap>", "line": 1006, "col": null}, {"name": "_find_and_load", "file": "<frozen importlib._bootstrap>", "line": 1027, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/decimal.py", "line": 3, "col": null}, {"name": "exec_module", "file": "<frozen importlib._bootstrap_external>", "line": 883, "col": null}, {"name": "_load_unlocked", "file": "<frozen importlib._bootstrap>", "line": 688, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/logger/__init__.py", "line": 3, "col": null}, {"name": "_find_and_load_unlocked", "file": "<frozen importlib._bootstrap>", "line": 992, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/__init__.py", "line": 9, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 10, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_parse.py", "line": 230, "col": null}, {"name": "parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_parse.py", "line": 947, "col": null}, {"name": "compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_compile.py", "line": 788, "col": null}, {"name": "_compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/re.py", "line": 303, "col": null}, {"name": "compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/re.py", "line": 251, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/platform.py", "line": 248, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/numpy/core/_add_newdocs_scalars.py", "line": 9, "col": null}, {"name": "_handle_fromlist", "file": "<frozen importlib._bootstrap>", "line": 1078, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/numpy/core/__init__.py", "line": 102, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/numpy/__init__.py", "line": 141, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/__init__.py", "line": 11, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/logger/logger.py", "line": 11, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/logger/__init__.py", "line": 14, "col": null}, {"name": "tell", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_parse.py", "line": 288, "col": null}, {"name": "_parse_sub", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_parse.py", "line": 442, "col": null}, {"name": "parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_parse.py", "line": 955, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/numpy/core/_internal.py", "line": 580, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/numpy/core/__init__.py", "line": 104, "col": null}, {"name": "_optimize_charset", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_compile.py", "line": 387, "col": null}, {"name": "_compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_compile.py", "line": 136, "col": null}, {"name": "_compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_compile.py", "line": 164, "col": null}, {"name": "_compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_compile.py", "line": 184, "col": null}, {"name": "_code", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_compile.py", "line": 631, "col": null}, {"name": "compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/sre_compile.py", "line": 792, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/locale.py", "line": 180, "col": null}, {"name": "exec_module", "file": "<frozen importlib._bootstrap_external>", "line": 1184, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/_libs/tslibs/__init__.py", "line": 37, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/_libs/__init__.py", "line": 13, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/util/_decorators.py", "line": 14, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/util/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/compat/numpy/__init__.py", "line": 4, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/compat/__init__.py", "line": 18, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/__init__.py", "line": 24, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyarrow/__init__.py", "line": 61, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/compat/pyarrow.py", "line": 8, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/compat/__init__.py", "line": 22, "col": null}, {"name": "_inherit_from_data", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/indexes/extension.py", "line": 70, "col": null}, {"name": "wrapper", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/indexes/extension.py", "line": 130, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/indexes/category.py", "line": 84, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/indexes/api.py", "line": 25, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/indexing.py", "line": 68, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/generic.py", "line": 131, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/frame.py", "line": 172, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/groupby/generic.py", "line": 76, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/groupby/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/core/api.py", "line": 47, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/__init__.py", "line": 48, "col": null}, {"name": "_compile_bytecode", "file": "<frozen importlib._bootstrap_external>", "line": 672, "col": null}, {"name": "get_code", "file": "<frozen importlib._bootstrap_external>", "line": 1015, "col": null}, {"name": "exec_module", "file": "<frozen importlib._bootstrap_external>", "line": 879, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/io/json/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/io/api.py", "line": 14, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/__init__.py", "line": 141, "col": null}, {"name": "dedent", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/textwrap.py", "line": 439, "col": null}, {"name": "decorator", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/util/_decorators.py", "line": 395, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/io/sas/sasreader.py", "line": 83, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/io/sas/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pandas/io/api.py", "line": 30, "col": null}, {"name": "_compile_and_eval", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/attr/_make.py", "line": 222, "col": null}, {"name": "_linecache_and_compile", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/attr/_make.py", "line": 256, "col": null}, {"name": "_eval_snippets", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/attr/_make.py", "line": 769, "col": null}, {"name": "build_class", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/attr/_make.py", "line": 784, "col": null}, {"name": "wrap", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/attr/_make.py", "line": 1521, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/attr/validators.py", "line": 380, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/attr/__init__.py", "line": 10, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client.py", "line": 35, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/__init__.py", "line": 6, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/data_types.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/utils.py", "line": 13, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/trade_fee.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/settings.py", "line": 12, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/client_config_map.py", "line": 15, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 11, "col": null}, {"name": "load_default_certs", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/ssl.py", "line": 593, "col": null}, {"name": "create_default_context", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/ssl.py", "line": 771, "col": null}, {"name": "_make_ssl_context", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/connector.py", "line": 779, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/connector.py", "line": 794, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client.py", "line": 79, "col": null}, {"name": "_execute_child", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/subprocess.py", "line": 1796, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/subprocess.py", "line": 971, "col": null}, {"name": "run", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/subprocess.py", "line": 503, "col": null}, {"name": "check_output", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/subprocess.py", "line": 421, "col": null}, {"name": "check_dev_mode", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/__init__.py", "line": 86, "col": null}, {"name": "get_strategy_list", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/__init__.py", "line": 176, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/settings.py", "line": 564, "col": null}, {"name": "_create_fn", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/dataclasses.py", "line": 432, "col": null}, {"name": "_init_fn", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/dataclasses.py", "line": 579, "col": null}, {"name": "_process_class", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/dataclasses.py", "line": 1040, "col": null}, {"name": "wrap", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/dataclasses.py", "line": 1175, "col": null}, {"name": "dataclass", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/dataclasses.py", "line": 1184, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/event/events.py", "line": 337, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/client_config_map.py", "line": 16, "col": null}, {"name": "_fill_cache", "file": "<frozen importlib._bootstrap_external>", "line": 1591, "col": null}, {"name": "find_spec", "file": "<frozen importlib._bootstrap_external>", "line": 1548, "col": null}, {"name": "_get_spec", "file": "<frozen importlib._bootstrap_external>", "line": 1411, "col": null}, {"name": "find_spec", "file": "<frozen importlib._bootstrap_external>", "line": 1439, "col": null}, {"name": "_find_spec", "file": "<frozen importlib._bootstrap>", "line": 945, "col": null}, {"name": "_find_and_load_unlocked", "file": "<frozen importlib._bootstrap>", "line": 1002, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/client/chain/grpc/chain_grpc_auth_api.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/async_client.py", "line": 11, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/__init__.py", "line": 5, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/injective_v2/injective_v2_utils.py", "line": 8, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/derivative/injective_v2_perpetual/injective_v2_perpetual_utils.py", "line": 8, "col": null}, {"name": "_gcd_import", "file": "<frozen importlib._bootstrap>", "line": 1050, "col": null}, {"name": "import_module", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/importlib/__init__.py", "line": 126, "col": null}, {"name": "create_connector_settings", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/settings.py", "line": 360, "col": null}, {"name": "get_connector_settings", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/settings.py", "line": 447, "col": null}, {"name": "fee_overrides_dict", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/fee_overrides_config_map.py", "line": 12, "col": null}, {"name": "init_fee_overrides_config", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/fee_overrides_config_map.py", "line": 29, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/fee_overrides_config_map.py", "line": 32, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/trade_fee_schema_loader.py", "line": 3, "col": null}, {"name": "_get_module_lock", "file": "<frozen importlib._bootstrap>", "line": 192, "col": null}, {"name": "_lock_unlock_module", "file": "<frozen importlib._bootstrap>", "line": 222, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/proto/ibc/core/commitment/v1/commitment_pb2.py", "line": 16, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/proto/ibc/core/client/v1/query_pb2.py", "line": 18, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/core/ibc/client/grpc/ibc_client_grpc_api.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/async_client.py", "line": 40, "col": null}, {"name": "__new__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/ssl.py", "line": 496, "col": null}, {"name": "create_urllib3_context", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/urllib3/util/ssl_.py", "line": 298, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/requests/adapters.py", "line": 80, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/requests/sessions.py", "line": 15, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/requests/api.py", "line": 11, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/requests/__init__.py", "line": 164, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/wallet.py", "line": 6, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/transaction.py", "line": 10, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyinjective/__init__.py", "line": 6, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/models/requests/ledger_entry.py", "line": 235, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/models/requests/get_aggregate_price.py", "line": 13, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/models/requests/__init__.py", "line": 22, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/models/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/clients/client.py", "line": 10, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/clients/async_client.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/clients/async_json_rpc_client.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/clients/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/account/main.py", "line": 5, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/account/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/account/main.py", "line": 6, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/account/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/xrpl/xrpl_utils.py", "line": 9, "col": null}, {"name": "_signature_is_functionlike", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/inspect.py", "line": 2004, "col": null}, {"name": "_signature_from_callable", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/inspect.py", "line": 2460, "col": null}, {"name": "from_callable", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/inspect.py", "line": 3002, "col": null}, {"name": "signature", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/inspect.py", "line": 3254, "col": null}, {"name": "_process_class", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/dataclasses.py", "line": 1093, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/anyio/streams/memory.py", "line": 37, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/anyio/_core/_streams.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/anyio/__init__.py", "line": 42, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpcore/_synchronization.py", "line": 18, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpcore/_sync/connection.py", "line": 12, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpcore/_sync/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpcore/_api.py", "line": 5, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpcore/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpx/_transports/default.py", "line": 30, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpx/_client.py", "line": 30, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpx/_api.py", "line": 4, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/httpx/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/clients/json_rpc_base.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/xrpl/asyncio/clients/async_json_rpc_client.py", "line": 3, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/xml/parsers/expat.py", "line": 4, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/plistlib.py", "line": 70, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pkg_resources/__init__.py", "line": 42, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_keyfile/__init__.py", "line": 3, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_account/account.py", "line": 18, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_account/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_crypt.py", "line": 5, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 12, "col": null}, {"name": "_mac_ver_xml", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/platform.py", "line": 426, "col": null}, {"name": "mac_ver", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/platform.py", "line": 450, "col": null}, {"name": "_macos_vers", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pkg_resources/__init__.py", "line": 426, "col": null}, {"name": "get_supported_platform", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pkg_resources/__init__.py", "line": 203, "col": null}, {"name": "Environment", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pkg_resources/__init__.py", "line": 1144, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pkg_resources/__init__.py", "line": 1138, "col": null}, {"name": "_add_declaration_specifier", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pycparser/c_parser.py", "line": 359, "col": null}, {"name": "p_declaration_specifiers_5", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pycparser/c_parser.py", "line": 838, "col": null}, {"name": "parseopt_notrack", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pycparser/ply/yacc.py", "line": 1118, "col": null}, {"name": "parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pycparser/ply/yacc.py", "line": 331, "col": null}, {"name": "parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pycparser/c_parser.py", "line": 147, "col": null}, {"name": "_parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/cffi/cparser.py", "line": 337, "col": null}, {"name": "_internal_parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/cffi/cparser.py", "line": 395, "col": null}, {"name": "parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/cffi/cparser.py", "line": 390, "col": null}, {"name": "_cdef", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/cffi/api.py", "line": 126, "col": null}, {"name": "cdef", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/cffi/api.py", "line": 112, "col": null}, {"name": "load_lib", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/Crypto/Util/_raw_api.py", "line": 106, "col": null}, {"name": "load_pycryptodome_raw_lib", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/Crypto/Util/_raw_api.py", "line": 312, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/Crypto/Cipher/AES.py", "line": 63, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_keyfile/keyfile.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_keyfile/__init__.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/xml/dom/minidom.py", "line": 22, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/formatted_text/html.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/formatted_text/__init__.py", "line": 23, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/completion/base.py", "line": 9, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/completion/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/buffer.py", "line": 36, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 42, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/__init__.py", "line": 16, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/config_command.py", "line": 6, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/hummingbot_application.py", "line": 9, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 20, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/scipy/sparse/csgraph/_validation.py", "line": 3, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/scipy/sparse/csgraph/__init__.py", "line": 186, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/scipy/sparse/__init__.py", "line": 283, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/scipy/optimize/_optimize.py", "line": 33, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/scipy/optimize/__init__.py", "line": 404, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/avellaneda_market_making/__init__.py", "line": 3, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/conf_migration.py", "line": 27, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/__init__.py", "line": 10, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/gateway_command.py", "line": 22, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/config_command.py", "line": 8, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/avellaneda_market_making/avellaneda_market_making_config_map_pydantic.py", "line": 56, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ccxt/base/types.py", "line": 34, "col": null}, {"name": "ImplicitAPI", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ccxt/abstract/binanceusdm.py", "line": 142, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ccxt/abstract/binanceusdm.py", "line": 4, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ccxt/binanceusdm.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ccxt/__init__.py", "line": 94, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/data_feed/candles_feed/ccxt_candles/ccxt_candles.py", "line": 5, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/data_feed/candles_feed/ccxt_candles/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/data_feed/candles_feed/candles_factory.py", "line": 9, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/data_feed/market_data_provider.py", "line": 18, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy_v2/controllers/controller_base.py", "line": 15, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy_v2/controllers/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/markets_recorder.py", "line": 50, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/strategy_v2_base.py", "line": 16, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/completer.py", "line": 28, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ccxt/async_support/__init__.py", "line": 159, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ccxt/pro/__init__.py", "line": 11, "col": null}, {"name": "take_using_weights", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/utils.py", "line": 280, "col": null}, {"name": "_divide_widths", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 659, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 691, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2639, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 399, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 809, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 716, "col": null}, {"name": "render", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 673, "col": null}, {"name": "run_in_context", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 535, "col": null}, {"name": "_redraw", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 552, "col": null}, {"name": "_run_async", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 728, "col": null}, {"name": "_run_async2", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 792, "col": null}, {"name": "run_async", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 816, "col": null}, {"name": "_run", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 80, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1909, "col": null}, {"name": "run_forever", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 603, "col": null}, {"name": "run_until_complete", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 636, "col": null}, {"name": "run", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 891, "col": null}, {"name": "login_prompt", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/__init__.py", "line": 61, "col": null}, {"name": "main", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 102, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 108, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 104, "col": null}, {"name": "call_at", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 736, "col": null}, {"name": "call_later", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 721, "col": null}, {"name": "sleep", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/tasks.py", "line": 601, "col": null}, {"name": "_poll_output_size", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 1020, "col": null}, {"name": "create_future", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 429, "col": null}, {"name": "sleep", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/tasks.py", "line": 600, "col": null}, {"name": "cancel", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 150, "col": null}, {"name": "sleep", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/tasks.py", "line": 607, "col": null}, {"name": "_pbkdf2_hash", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_keyfile/keyfile.py", "line": 240, "col": null}, {"name": "_derive_pbkdf_key", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_keyfile/keyfile.py", "line": 202, "col": null}, {"name": "_decode_keyfile_json_v3", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_keyfile/keyfile.py", "line": 165, "col": null}, {"name": "decode_keyfile_json", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_keyfile/keyfile.py", "line": 56, "col": null}, {"name": "decrypt", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/eth_account/account.py", "line": 176, "col": null}, {"name": "decrypt_secret_value", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_crypt.py", "line": 59, "col": null}, {"name": "validate_password", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_crypt.py", "line": 74, "col": null}, {"name": "login", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/security.py", "line": 52, "col": null}, {"name": "login_prompt", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/__init__.py", "line": 65, "col": null}, {"name": "decrypt_all_secure_data", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 237, "col": null}, {"name": "validate_model", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 199, "col": null}, {"name": "_load_yml_data_into_map", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 750, "col": null}, {"name": "load_connector_config_map_from_file", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 668, "col": null}, {"name": "decrypt_connector_config", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/security.py", "line": 71, "col": null}, {"name": "decrypt_all", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/security.py", "line": 65, "col": null}, {"name": "run", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/concurrent/futures/thread.py", "line": 58, "col": null}, {"name": "_worker", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/concurrent/futures/thread.py", "line": 83, "col": null}, {"name": "run", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/threading.py", "line": 953, "col": null}, {"name": "_bootstrap_inner", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/threading.py", "line": 1016, "col": null}, {"name": "_bootstrap", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/threading.py", "line": 973, "col": null}, {"name": "forward", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/reader.py", "line": 104, "col": null}, {"name": "scan_to_next_token", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/scanner.py", "line": 777, "col": null}, {"name": "fetch_more_tokens", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/scanner.py", "line": 159, "col": null}, {"name": "check_token", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/scanner.py", "line": 116, "col": null}, {"name": "parse_block_mapping_value", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/parser.py", "line": 449, "col": null}, {"name": "check_event", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/parser.py", "line": 98, "col": null}, {"name": "compose_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/composer.py", "line": 64, "col": null}, {"name": "compose_mapping_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/composer.py", "line": 133, "col": null}, {"name": "compose_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/composer.py", "line": 84, "col": null}, {"name": "compose_document", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/composer.py", "line": 55, "col": null}, {"name": "get_single_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/composer.py", "line": 36, "col": null}, {"name": "get_single_data", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/constructor.py", "line": 49, "col": null}, {"name": "load", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/__init__.py", "line": 81, "col": null}, {"name": "safe_load", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/yaml/__init__.py", "line": 125, "col": null}, {"name": "read_yml_file", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 629, "col": null}, {"name": "connector_name_from_file", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 611, "col": null}, {"name": "load_connector_config_map_from_file", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 665, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1345, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1506, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/widgets/base.py", "line": 652, "col": null}, {"name": "generate_layout", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/layout.py", "line": 200, "col": null}, {"name": "__init__", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/hummingbot_cli.py", "line": 72, "col": null}, {"name": "__init__", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/hummingbot_application.py", "line": 117, "col": null}, {"name": "main_application", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/hummingbot_application.py", "line": 68, "col": null}, {"name": "main_async", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 66, "col": null}, {"name": "main", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/bin/hummingbot.py", "line": 104, "col": null}, {"name": "_decompose", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyunormalize/normalization.py", "line": 390, "col": null}, {"name": "NFD", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyunormalize/normalization.py", "line": 240, "col": null}, {"name": "_extract_valid_codepoints", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ens/_normalization.py", "line": 160, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ens/_normalization.py", "line": 216, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ens/utils.py", "line": 39, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ens/base_ens.py", "line": 18, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ens/async_ens.py", "line": 36, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ens/__init__.py", "line": 3, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/web3/_utils/ens.py", "line": 22, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/web3/_utils/abi.py", "line": 74, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/web3/_utils/encoding.py", "line": 40, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/web3/providers/async_base.py", "line": 18, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/web3/providers/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/web3/__init__.py", "line": 4, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/lyra_v2_action_signing/module_data/deposit.py", "line": 4, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/lyra_v2_action_signing/module_data/__init__.py", "line": 2, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/lyra_v2_action_signing/__init__.py", "line": 1, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/derivative/derive_perpetual/derive_perpetual_auth.py", "line": 7, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/derivative/derive_perpetual/derive_perpetual_api_user_stream_data_source.py", "line": 8, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/derivative/derive_perpetual/derive_perpetual_derivative.py", "line": 17, "col": null}, {"name": "non_trading_connector_instance_with_default_configuration", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/settings.py", "line": 300, "col": null}, {"name": "_fetch_pairs_from_connector_setting", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/utils/trading_pair_fetcher.py", "line": 40, "col": null}, {"name": "fetch_all", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/utils/trading_pair_fetcher.py", "line": 57, "col": null}, {"name": "safe_wrapper", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/utils/async_utils.py", "line": 9, "col": null}, {"name": "call_fetch_pairs", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/utils/trading_pair_fetcher.py", "line": 69, "col": null}, {"name": "getaddrinfo", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pycares/__init__.py", "line": 574, "col": null}, {"name": "getaddrinfo", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiodns/__init__.py", "line": 140, "col": null}, {"name": "resolve", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/resolver.py", "line": 105, "col": null}, {"name": "_resolve_host_with_throttle", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/connector.py", "line": 1020, "col": null}, {"name": "parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/email/parser.py", "line": 49, "col": null}, {"name": "parse", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/email/parser.py", "line": 73, "col": null}, {"name": "parsestr", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/email/parser.py", "line": 67, "col": null}, {"name": "parsestr", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/email/parser.py", "line": 76, "col": null}, {"name": "_parse_content_type", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/helpers.py", "line": 790, "col": null}, {"name": "content_type", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/helpers.py", "line": 800, "col": null}, {"name": "json", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/data_types.py", "line": 113, "col": null}, {"name": "execute_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 66, "col": null}, {"name": "get_current_server_time", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/binance/binance_web_utils.py", "line": 69, "col": null}, {"name": "update_server_time_offset_with_time_provider", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/time_synchronizer.py", "line": 66, "col": null}, {"name": "update_server_time_if_not_initialized", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/time_synchronizer.py", "line": 87, "col": null}, {"name": "pre_process", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/utils.py", "line": 106, "col": null}, {"name": "_pre_process_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 147, "col": null}, {"name": "call", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 139, "col": null}, {"name": "execute_request_and_get_response", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 125, "col": null}, {"name": "execute_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 55, "col": null}, {"name": "_api_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 987, "col": null}, {"name": "_api_get", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 944, "col": null}, {"name": "_make_trading_pairs_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 1167, "col": null}, {"name": "_initialize_trading_pair_symbol_map", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 1154, "col": null}, {"name": "<listcomp>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 157, "col": null}, {"name": "get_max_column_index", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 154, "col": null}, {"name": "_output_screen_diff", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 199, "col": null}, {"name": "render", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 688, "col": null}, {"name": "redraw", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 480, "col": null}, {"name": "schedule", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/eventloop/utils.py", "line": 72, "col": null}, {"name": "raw_decode", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/json/decoder.py", "line": 353, "col": null}, {"name": "decode", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/json/decoder.py", "line": 337, "col": null}, {"name": "loads", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/json/__init__.py", "line": 346, "col": null}, {"name": "json", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client_reqrep.py", "line": 1295, "col": null}, {"name": "json", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/data_types.py", "line": 121, "col": null}, {"name": "_api_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/bybit/bybit_exchange.py", "line": 585, "col": null}, {"name": "_make_trading_pairs_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/bybit/bybit_exchange.py", "line": 616, "col": null}, {"name": "_output_screen_diff", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 198, "col": null}, {"name": "fill_area", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/screen.py", "line": 303, "col": null}, {"name": "_apply_style", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2201, "col": null}, {"name": "_write_to_screen_at_index", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1924, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1752, "col": null}, {"name": "__hash__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/typing.py", "line": 1038, "col": null}, {"name": "inner", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/typing.py", "line": 309, "col": null}, {"name": "iteritems", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/bidict/_iter.py", "line": 30, "col": null}, {"name": "_update", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/bidict/_base.py", "line": 452, "col": null}, {"name": "put", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/bidict/_bidict.py", "line": 106, "col": null}, {"name": "__setitem__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/bidict/_bidict.py", "line": 80, "col": null}, {"name": "_initialize_trading_pair_symbols_from_exchange_info", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/gate_io/gate_io_exchange.py", "line": 511, "col": null}, {"name": "_initialize_trading_pair_symbol_map", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 1155, "col": null}, {"name": "read", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/input/posix_utils.py", "line": 72, "col": null}, {"name": "read_keys", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/input/vt100.py", "line": 100, "col": null}, {"name": "read_from_input", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 687, "col": null}, {"name": "callback_wrapper", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/input/vt100.py", "line": 168, "col": null}, {"name": "_divide_widths", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 667, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 571, "col": null}, {"name": "<listcomp>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 437, "col": null}, {"name": "_divide_heights", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 437, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 383, "col": null}, {"name": "is_true", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/filters/utils.py", "line": 41, "col": null}, {"name": "<lambda>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/custom_widgets.py", "line": 179, "col": null}, {"name": "__call__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/filters/base.py", "line": 210, "col": null}, {"name": "preferred_content_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1614, "col": null}, {"name": "_merge_dimensions", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1651, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1623, "col": null}, {"name": "<listcomp>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 578, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 577, "col": null}, {"name": "<listcomp>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 326, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 325, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 795, "col": null}, {"name": "get_line", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/custom_widgets.py", "line": 69, "col": null}, {"name": "get_processed_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 726, "col": null}, {"name": "translate_rowcol", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 788, "col": null}, {"name": "create_content", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 805, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 649, "col": null}, {"name": "preferred_content_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1616, "col": null}, {"name": "fill_area", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/screen.py", "line": 298, "col": null}, {"name": "_apply_style", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2211, "col": null}, {"name": "get_mark", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/reader.py", "line": 185, "col": null}, {"name": "scan_plain", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/scanner.py", "line": 1580, "col": null}, {"name": "fetch_plain", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/scanner.py", "line": 749, "col": null}, {"name": "fetch_more_tokens", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/scanner.py", "line": 315, "col": null}, {"name": "get_token", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/scanner.py", "line": 1836, "col": null}, {"name": "parse_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/parser.py", "line": 410, "col": null}, {"name": "parse_block_node_or_indentless_sequence", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/parser.py", "line": 337, "col": null}, {"name": "parse_block_mapping_value", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/parser.py", "line": 611, "col": null}, {"name": "check_event", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/parser.py", "line": 140, "col": null}, {"name": "compose_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/composer.py", "line": 111, "col": null}, {"name": "compose_mapping_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/composer.py", "line": 218, "col": null}, {"name": "compose_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/composer.py", "line": 138, "col": null}, {"name": "compose_document", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/composer.py", "line": 101, "col": null}, {"name": "get_single_node", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/composer.py", "line": 78, "col": null}, {"name": "get_single_data", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/constructor.py", "line": 111, "col": null}, {"name": "load", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/ruamel/yaml/main.py", "line": 343, "col": null}, {"name": "load_yml_into_cm_legacy", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 785, "col": null}, {"name": "load_strategy_config_map_from_file", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 654, "col": null}, {"name": "import_config_file", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/import_command.py", "line": 47, "col": null}, {"name": "search_buffer_control", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 593, "col": null}, {"name": "search_state", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 616, "col": null}, {"name": "_get_search_text", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 187, "col": null}, {"name": "apply_transformation", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 203, "col": null}, {"name": "apply_transformation", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 1000, "col": null}, {"name": "transform", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 706, "col": null}, {"name": "get_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 792, "col": null}, {"name": "get_height_for_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 225, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 662, "col": null}, {"name": "schedule", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/eventloop/utils.py", "line": 77, "col": null}, {"name": "current_buffer", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/layout.py", "line": 266, "col": null}, {"name": "current_buffer", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 404, "col": null}, {"name": "test", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/filters/app.py", "line": 67, "col": null}, {"name": "has_focus_filter", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/filters/app.py", "line": 96, "col": null}, {"name": "<genexpr>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/filters/base.py", "line": 126, "col": null}, {"name": "__call__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/filters/base.py", "line": 126, "col": null}, {"name": "apply_transformation", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 937, "col": null}, {"name": "_read_ready__data_received", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/selector_events.py", "line": 862, "col": null}, {"name": "_read_ready", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/selector_events.py", "line": 819, "col": null}, {"name": "do_handshake", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/ssl.py", "line": 975, "col": null}, {"name": "feed_ssldata", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 188, "col": null}, {"name": "data_received", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 534, "col": null}, {"name": "_read_ready__data_received", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/selector_events.py", "line": 876, "col": null}, {"name": "write_headers", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/http_writer.py", "line": 130, "col": null}, {"name": "send", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client_reqrep.py", "line": 771, "col": null}, {"name": "_request", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client.py", "line": 691, "col": null}, {"name": "call", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/rest_connection.py", "line": 10, "col": null}, {"name": "wait_for", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/tasks.py", "line": 408, "col": null}, {"name": "call", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 141, "col": null}, {"name": "_api_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/derivative/bybit_perpetual/bybit_perpetual_derivative.py", "line": 886, "col": null}, {"name": "_fetch_available_balance", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/derivative/bybit_perpetual/bybit_perpetual_derivative.py", "line": 430, "col": null}, {"name": "_update_balances", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/derivative/bybit_perpetual/bybit_perpetual_derivative.py", "line": 426, "col": null}, {"name": "_update_balances", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/user/user_balances.py", "line": 53, "col": null}, {"name": "add_exchange", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/user/user_balances.py", "line": 88, "col": null}, {"name": "update_exchange_balance", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/user/user_balances.py", "line": 109, "col": null}, {"name": "write", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/ssl.py", "line": 926, "col": null}, {"name": "feed_appdata", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 261, "col": null}, {"name": "_process_write_backlog", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 680, "col": null}, {"name": "_write_appdata", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 604, "col": null}, {"name": "write", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 392, "col": null}, {"name": "_write", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/http_writer.py", "line": 77, "col": null}, {"name": "write_headers", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/http_writer.py", "line": 131, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyarrow/fs.py", "line": 53, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyarrow/parquet/core.py", "line": 47, "col": null}, {"name": "<module>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/pyarrow/parquet/__init__.py", "line": 20, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/data_collector/data_collector.py", "line": 12, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/xemm.py", "line": 49, "col": null}, {"name": "<module>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/start.py", "line": 17, "col": null}, {"name": "get_strategy_starter_file", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/config/config_helpers.py", "line": 597, "col": null}, {"name": "_initialize_strategy", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/start_command.py", "line": 273, "col": null}, {"name": "start_check", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/start_command.py", "line": 125, "col": null}, {"name": "copy_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2084, "col": null}, {"name": "copy", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2104, "col": null}, {"name": "_copy_body", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2110, "col": null}, {"name": "_write_to_screen_at_index", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1794, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/dimension.py", "line": 53, "col": null}, {"name": "to_dimension", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/dimension.py", "line": 188, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1624, "col": null}, {"name": "<listcomp>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 705, "col": null}, {"name": "write_to_screen", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 704, "col": null}, {"name": "get_app", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/current.py", "line": 102, "col": null}, {"name": "_get_formatted_text_cached", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 359, "col": null}, {"name": "create_content", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 394, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 381, "col": null}, {"name": "_output_screen_diff", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 204, "col": null}, {"name": "_run_clock", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/command/start_command.py", "line": 42, "col": null}, {"name": "copy_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2066, "col": null}, {"name": "do_handshake", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 122, "col": null}, {"name": "_process_write_backlog", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 682, "col": null}, {"name": "_start_handshake", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 619, "col": null}, {"name": "connection_made", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 486, "col": null}, {"name": "get_cursor_position", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 417, "col": null}, {"name": "create_content", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 426, "col": null}, {"name": "_write_to_screen_at_index", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1776, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2624, "col": null}, {"name": "preferred_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2625, "col": null}, {"name": "copy", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2098, "col": null}, {"name": "_get_protocol_attrs", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/typing.py", "line": 1415, "col": null}, {"name": "_is_callable_members_only", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/typing.py", "line": 1427, "col": null}, {"name": "__instancecheck__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/typing.py", "line": 1502, "col": null}, {"name": "iteritems", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/bidict/_iter.py", "line": 26, "col": null}, {"name": "_request_order_book_snapshot", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/gate_io/gate_io_api_order_book_data_source.py", "line": 64, "col": null}, {"name": "_order_book_snapshot", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/gate_io/gate_io_api_order_book_data_source.py", "line": 41, "col": null}, {"name": "get_new_order_book", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/order_book_tracker_data_source.py", "line": 65, "col": null}, {"name": "_initial_order_book_for_trading_pair", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/order_book_tracker.py", "line": 173, "col": null}, {"name": "_init_order_books", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/order_book_tracker.py", "line": 180, "col": null}, {"name": "get_line_height", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2340, "col": null}, {"name": "get_topmost_visible", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2422, "col": null}, {"name": "_scroll_when_linewrapping", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2431, "col": null}, {"name": "_scroll", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2322, "col": null}, {"name": "_write_to_screen_at_index", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1783, "col": null}, {"name": "preferred_width", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1596, "col": null}, {"name": "<listcomp>", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 632, "col": null}, {"name": "_divide_widths", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 632, "col": null}, {"name": "copy_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2004, "col": null}, {"name": "_copy_margin", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2311, "col": null}, {"name": "_write_to_screen_at_index", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1920, "col": null}, {"name": "_make_trading_rules_request", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 1163, "col": null}, {"name": "_update_trading_rules", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 935, "col": null}, {"name": "<listcomp>", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/xemm.py", "line": 349, "col": null}, {"name": "tick", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/xemm.py", "line": 349, "col": null}, {"name": "take_using_weights", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/utils.py", "line": 283, "col": null}, {"name": "_divide_widths", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 668, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1882, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 289, "col": null}, {"name": "makeRecord", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1591, "col": null}, {"name": "_log", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1622, "col": null}, {"name": "info", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1477, "col": null}, {"name": "_trigger_order_creation", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 396, "col": null}, {"name": "_process_order_update", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 294, "col": null}, {"name": "copy_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2051, "col": null}, {"name": "fill_area", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/screen.py", "line": 305, "col": null}, {"name": "_get_binance_prices", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/rate_oracle/sources/binance_rate_source.py", "line": 58, "col": null}, {"name": "gather_events", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 582, "col": null}, {"name": "_update_invalidate_events", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 585, "col": null}, {"name": "run_in_context", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 542, "col": null}, {"name": "walk", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/layout.py", "line": 417, "col": null}, {"name": "walk", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/layout.py", "line": 345, "col": null}, {"name": "find_all_windows", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/layout.py", "line": 79, "col": null}, {"name": "find_all_controls", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/layout.py", "line": 84, "col": null}, {"name": "gather_events", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/application/application.py", "line": 581, "col": null}, {"name": "json", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/http_websocket.py", "line": 133, "col": null}, {"name": "_build_resp", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 150, "col": null}, {"name": "receive", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 69, "col": null}, {"name": "iter_messages", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/ws_assistant.py", "line": 71, "col": null}, {"name": "_process_websocket_messages", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/order_book_tracker_data_source.py", "line": 233, "col": null}, {"name": "receive", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 66, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1895, "col": null}, {"name": "read", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/ssl.py", "line": 917, "col": null}, {"name": "feed_ssldata", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 199, "col": null}, {"name": "_call_soon", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 773, "col": null}, {"name": "call_soon", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 757, "col": null}, {"name": "_set_result_unless_cancelled", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/futures.py", "line": 313, "col": null}, {"name": "copy_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2087, "col": null}, {"name": "_scroll_without_linewrapping", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2540, "col": null}, {"name": "acquire", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 916, "col": null}, {"name": "handle", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 966, "col": null}, {"name": "callHandlers", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1696, "col": null}, {"name": "handle", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1634, "col": null}, {"name": "_log", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1624, "col": null}, {"name": "usesTime", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 647, "col": null}, {"name": "format", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 679, "col": null}, {"name": "format", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 943, "col": null}, {"name": "emit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1100, "col": null}, {"name": "emit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1218, "col": null}, {"name": "emit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/handlers.py", "line": 75, "col": null}, {"name": "handle", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 968, "col": null}, {"name": "_format", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 436, "col": null}, {"name": "format", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 440, "col": null}, {"name": "formatMessage", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 650, "col": null}, {"name": "format", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 681, "col": null}, {"name": "__lt__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 121, "col": null}, {"name": "_set_result_unless_cancelled", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/futures.py", "line": 311, "col": null}, {"name": "_timer_handle_cancelled", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1830, "col": null}, {"name": "_reject", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/async_timeout/__init__.py", "line": 202, "col": null}, {"name": "_do_exit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/async_timeout/__init__.py", "line": 268, "col": null}, {"name": "__aexit__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/async_timeout/__init__.py", "line": 179, "col": null}, {"name": "receive", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client_ws.py", "line": 312, "col": null}, {"name": "_read_message", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 83, "col": null}, {"name": "_build_resp", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 146, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1892, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 156, "col": null}, {"name": "apply_transformation", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 243, "col": null}, {"name": "fill_area", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/screen.py", "line": 304, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1880, "col": null}, {"name": "_time", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/order_book_tracker_data_source.py", "line": 258, "col": null}, {"name": "_parse_order_book_diff_message", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/kucoin/kucoin_api_order_book_data_source.py", "line": 102, "col": null}, {"name": "listen_for_order_book_diffs", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/order_book_tracker_data_source.py", "line": 105, "col": null}, {"name": "__lt__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 122, "col": null}, {"name": "call_at", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 739, "col": null}, {"name": "_reschedule", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/async_timeout/__init__.py", "line": 252, "col": null}, {"name": "_do_enter", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/async_timeout/__init__.py", "line": 258, "col": null}, {"name": "__aenter__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/async_timeout/__init__.py", "line": 170, "col": null}, {"name": "take_using_weights", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/utils.py", "line": 279, "col": null}, {"name": "_divide_heights", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 464, "col": null}, {"name": "check_balances_and_quotes", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/xemm.py", "line": 1186, "col": null}, {"name": "main_tick_logic", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/xemm.py", "line": 326, "col": null}, {"name": "tick", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/xemm.py", "line": 360, "col": null}, {"name": "check_balances_and_quotes", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/strategy/xemm/xemm.py", "line": 1195, "col": null}, {"name": "_ureduce", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/numpy/lib/function_base.py", "line": 3700, "col": null}, {"name": "median", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/numpy/lib/function_base.py", "line": 3816, "col": null}, {"name": "median", "file": "<__array_function__ internals>", "line": 180, "col": null}, {"name": "time_offset_ms", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/time_synchronizer.py", "line": 38, "col": null}, {"name": "time", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/time_synchronizer.py", "line": 55, "col": null}, {"name": "authentication_headers", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/kucoin/kucoin_auth.py", "line": 62, "col": null}, {"name": "rest_authenticate", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/kucoin/kucoin_auth.py", "line": 36, "col": null}, {"name": "_authenticate", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 152, "col": null}, {"name": "call", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/rest_assistant.py", "line": 140, "col": null}, {"name": "_api_post", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 948, "col": null}, {"name": "_place_order", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/kucoin/kucoin_exchange.py", "line": 212, "col": null}, {"name": "_place_order_and_process_update", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 546, "col": null}, {"name": "_create_order", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 525, "col": null}, {"name": "cancel", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 151, "col": null}, {"name": "do_commit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/default.py", "line": 686, "col": null}, {"name": "_commit_impl", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/base.py", "line": 1094, "col": null}, {"name": "_connection_commit_impl", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/base.py", "line": 2630, "col": null}, {"name": "_do_commit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/base.py", "line": 2659, "col": null}, {"name": "commit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/base.py", "line": 2469, "col": null}, {"name": "commit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/session.py", "line": 839, "col": null}, {"name": "__exit__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/util.py", "line": 233, "col": null}, {"name": "_did_create_order", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/markets_recorder.py", "line": 315, "col": null}, {"name": "__call__", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/event/event_forwarder.py", "line": 24, "col": null}, {"name": "_trigger_created_event", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 310, "col": null}, {"name": "_trigger_order_creation", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 397, "col": null}, {"name": "set_result", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/helpers.py", "line": 822, "col": null}, {"name": "feed_data", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/streams.py", "line": 629, "col": null}, {"name": "feed_data", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/streams.py", "line": 678, "col": null}, {"name": "_feed_data", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/http_websocket.py", "line": 424, "col": null}, {"name": "feed_data", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/http_websocket.py", "line": 330, "col": null}, {"name": "data_received", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client_proto.py", "line": 247, "col": null}, {"name": "data_received", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 552, "col": null}, {"name": "copy_line", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 1990, "col": null}, {"name": "_read_ready__data_received", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/selector_events.py", "line": 859, "col": null}, {"name": "time", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 703, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1877, "col": null}, {"name": "put_nowait", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/queues.py", "line": 148, "col": null}, {"name": "_parse_order_book_diff_message", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/kucoin/kucoin_api_order_book_data_source.py", "line": 119, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1883, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1894, "col": null}, {"name": "_divide_widths", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 665, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1871, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1884, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1893, "col": null}, {"name": "copy", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 2096, "col": null}, {"name": "feed_ssldata", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/sslproto.py", "line": 186, "col": null}, {"name": "decode", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/json/decoder.py", "line": 338, "col": null}, {"name": "__call__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/utils.py", "line": 78, "col": null}, {"name": "fire", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/utils.py", "line": 83, "col": null}, {"name": "_text_changed", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/buffer.py", "line": 547, "col": null}, {"name": "set_document", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/buffer.py", "line": 622, "col": null}, {"name": "document", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/buffer.py", "line": 591, "col": null}, {"name": "log", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/custom_widgets.py", "line": 254, "col": null}, {"name": "write_and_flush", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/stdout_redirection.py", "line": 64, "col": null}, {"name": "get_max_column_index", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 160, "col": null}, {"name": "_parse_order_book_diff_message", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange/kucoin/kucoin_api_order_book_data_source.py", "line": 105, "col": null}, {"name": "_check_msg_types", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 96, "col": null}, {"name": "_process_message", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 89, "col": null}, {"name": "receive", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 67, "col": null}, {"name": "do_execute", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/default.py", "line": 736, "col": null}, {"name": "_execute_context", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/base.py", "line": 1914, "col": null}, {"name": "_execute_clauseelement", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/base.py", "line": 1577, "col": null}, {"name": "_execute_on_connection", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/sql/elements.py", "line": 334, "col": null}, {"name": "_execute_20", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/engine/base.py", "line": 1710, "col": null}, {"name": "_emit_update_statements", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/persistence.py", "line": 1001, "col": null}, {"name": "save_obj", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/persistence.py", "line": 237, "col": null}, {"name": "execute", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/unitofwork.py", "line": 630, "col": null}, {"name": "execute", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/unitofwork.py", "line": 456, "col": null}, {"name": "_flush", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/session.py", "line": 3551, "col": null}, {"name": "flush", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/session.py", "line": 3449, "col": null}, {"name": "_autoflush", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/session.py", "line": 2253, "col": null}, {"name": "orm_pre_session_exec", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/context.py", "line": 312, "col": null}, {"name": "execute", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/session.py", "line": 1665, "col": null}, {"name": "_iter", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/query.py", "line": 2916, "col": null}, {"name": "one_or_none", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/sqlalchemy/orm/query.py", "line": 2850, "col": null}, {"name": "get_market_states", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/markets_recorder.py", "line": 299, "col": null}, {"name": "save_market_states", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/markets_recorder.py", "line": 271, "col": null}, {"name": "_update_order_status", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/markets_recorder.py", "line": 483, "col": null}, {"name": "_did_cancel_order", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/markets_recorder.py", "line": 489, "col": null}, {"name": "_trigger_cancelled_event", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 327, "col": null}, {"name": "_trigger_order_completion", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 427, "col": null}, {"name": "_process_order_update", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 295, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 33, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 105, "col": null}, {"name": "_output_screen_diff", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 211, "col": null}, {"name": "_order_book_diff_router", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/order_book_tracker.py", "line": 199, "col": null}, {"name": "_add_callback", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1820, "col": null}, {"name": "_process_events", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/selector_events.py", "line": 607, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1872, "col": null}, {"name": "_divide_widths", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 666, "col": null}, {"name": "write", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/stdout_redirection.py", "line": 90, "col": null}, {"name": "emit", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/logging/__init__.py", "line": 1103, "col": null}, {"name": "_trigger_order_completion", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/client_order_tracker.py", "line": 428, "col": null}, {"name": "_write", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/stdout_redirection.py", "line": 74, "col": null}, {"name": "write", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/stdout_redirection.py", "line": 92, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 157, "col": null}, {"name": "apply_transformation", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/processors.py", "line": 489, "col": null}, {"name": "receive", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/aiohttp/client_ws.py", "line": 313, "col": null}, {"name": "_output_screen_diff", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 206, "col": null}, {"name": "_check_msg_closed_type", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 111, "col": null}, {"name": "_check_msg_types", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/web_assistant/connections/ws_connection.py", "line": 95, "col": null}, {"name": "_output_screen_diff", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/renderer.py", "line": 203, "col": null}, {"name": "_run_once", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/base_events.py", "line": 1878, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 38, "col": null}, {"name": "_run", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/asyncio/events.py", "line": 79, "col": null}, {"name": "cpu_count_logical", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/psutil/_psosx.py", "line": 159, "col": null}, {"name": "cpu_count", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/psutil/__init__.py", "line": 1596, "col": null}, {"name": "cpu_percent", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/psutil/__init__.py", "line": 1000, "col": null}, {"name": "start_process_monitor", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/client/ui/interface_utils.py", "line": 49, "col": null}, {"name": "last_recv_time", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/core/data_type/user_stream_tracker.py", "line": 30, "col": null}, {"name": "_get_poll_interval", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 1172, "col": null}, {"name": "tick", "file": "/Users/<USER>/PycharmProjects/hummingbot_private/hummingbot/connector/exchange_py_base.py", "line": 255, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/weakref.py", "line": 354, "col": null}, {"name": "__setitem__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/weakref.py", "line": 168, "col": null}, {"name": "__init__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/document.py", "line": 131, "col": null}, {"name": "__missing__", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/cache.py", "line": 98, "col": null}, {"name": "document", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/buffer.py", "line": 579, "col": null}, {"name": "create_content", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/controls.py", "line": 779, "col": null}, {"name": "_divide_heights", "file": "/Users/<USER>/opt/anaconda3/envs/hummingbot_private/lib/python3.10/site-packages/prompt_toolkit/layout/containers.py", "line": 462, "col": null}]}, "activeProfileIndex": null, "exporter": "py-spy@0.4.0", "name": "py-spy profile"}