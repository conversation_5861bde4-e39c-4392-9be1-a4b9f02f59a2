from decimal import Decimal

from hummingbot.core.data_type.common import OrderType, TradeType
from hummingbot.core.event.events import OrderFilledEvent
from hummingbot.smart_components.executors.trade_executor.data_types import TradeExecutorConfig, TradeExecutorStatus
from hummingbot.smart_components.executors.trade_executor.trade_executor import TradeExecutor
from hummingbot.strategy.market_trading_pair_tuple import MarketTradingPairTuple
from hummingbot.strategy.script_strategy_base import ScriptStrategyBase
from hummingbot.strategy_v2.executors.data_types import ConnectorPair


class TradeWithSmartComponent(ScriptStrategyBase):
    # Parameters
    markets_side_amount = {
        "ascend_ex": [("XCAD-USDT", "sell", Decimal("100000"))]
    }

    markets = {market: {token[0] for token in tokens} for market, tokens in markets_side_amount.items()}
    active_trade_dict = {market: {token[0]: False for token in tokens} for market, tokens in markets_side_amount.items()}
    trade_executor_dict = {market: {token[0]: None for token in tokens} for market, tokens in markets_side_amount.items()}
    is_done_dict = {market: {token[0]: False for token in tokens} for market, tokens in markets_side_amount.items()}

    def on_tick(self):

        for connector_name, tokens in self.markets.items():
            for pair in tokens:
                if not self.active_trade_dict[connector_name][pair] and not self.is_done_dict[connector_name][pair]:
                    amount = next(amount for token, side, amount in self.markets_side_amount[connector_name] if token == pair)
                    is_buy = next(True if side == "buy" else False for token, side, amount in self.markets_side_amount[connector_name] if token == pair)
                    self.start_trade(connector_name, pair, is_buy, amount)
                elif self.active_trade_dict[connector_name][pair] and self.trade_executor_dict[connector_name][pair].status in [TradeExecutorStatus.ACTIVE, TradeExecutorStatus.COMPLETED,
                                                                                                                                TradeExecutorStatus.FAILED]:
                    self.active_trade_dict[connector_name][pair] = False
                    self.trade_executor_dict[connector_name][pair].stop_trade()
                    self.trade_executor_dict[connector_name][pair] = None
                    self.is_done_dict[connector_name][pair] = True
                    self.logger().info(f"Trade for {connector_name} is done")

    def start_trade(self, connector_name, trading_pair, is_buy, amount):
        self.logger().info(f"Starting trade for {connector_name}, {trading_pair}, {amount}")
        market_trading_pair_tuple = MarketTradingPairTuple(market=self.connectors[connector_name], trading_pair=trading_pair, base_asset=trading_pair.split("-")[0],
                                                           quote_asset=trading_pair.split("-")[1])
        trade_config = TradeExecutorConfig(
            market=ConnectorPair(connector_name=connector_name, trading_pair=trading_pair),
            order_amount=amount,
            open_order_type=OrderType.LIMIT,
            close_order_type=OrderType.LIMIT,
            is_buy=is_buy,
            timestamp=self.current_timestamp
        )

        self.trade_executor_dict[connector_name][trading_pair] = TradeExecutor(strategy=self, config=trade_config)
        self.logger().info(f"trade_executor: {self.trade_executor_dict[connector_name][trading_pair]}")
        self.active_trade_dict[connector_name][trading_pair] = True

    def on_stop(self):
        for connector_name, tokens in self.markets.items():
            for pair in tokens:
                trade_executor = self.trade_executor_dict[connector_name][pair]
                if trade_executor is not None:
                    trade_executor.stop_trade()

    def did_fill_order(self, event: OrderFilledEvent):
        self.notify_hb_app(f"{'bought' if event.trade_type == TradeType.BUY else 'sold'} {event.amount} {event.trading_pair} at {event.price}")
