import csv
import os
import time
from decimal import Decimal
from typing import Dict, Optional

from hummingbot.client.config.global_config_map import global_config_map
from hummingbot.core.utils.async_utils import safe_ensure_future


class OrderTimingTracker:
    """
    Tracks the timing of order execution from decision to API request execution.
    Records the data in a CSV file with millisecond precision (0.001ms).
    
    Tracks four key timestamps:
    1. Decision time: When XEMM decides to place an order
    2. Creation time: When the order is created in the strategy
    3. Pre-API time: Just before the API call is made
    4. Execution time: When the API call completes
    """
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> "OrderTimingTracker":
        if cls._instance is None:
            cls._instance = OrderTimingTracker()
        return cls._instance
    
    def __init__(self):
        self._decision_timestamps: Dict[str, float] = {}
        self._order_creation_timestamps: Dict[str, float] = {}
        self._pre_api_timestamps: Dict[str, float] = {}
        self._csv_path = self._get_csv_path()
        self._ensure_csv_exists()
    
    def _get_csv_path(self) -> str:
        """Get the path to the CSV file for storing timing data."""
        data_path = global_config_map.get("data_path").value
        if data_path is None:
            data_path = "."
        
        return os.path.join(data_path, "order_execution_timing.csv")
    
    def _ensure_csv_exists(self) -> None:
        """Ensure the CSV file exists with proper headers."""
        if not os.path.exists(self._csv_path):
            with open(self._csv_path, "w", newline="") as f:
                writer = csv.writer(f)
                writer.writerow([
                    "timestamp",
                    "order_id", 
                    "exchange",
                    "trading_pair",
                    "side",
                    "order_type",
                    "price",
                    "amount",
                    "decision_time",
                    "creation_time",
                    "pre_api_time",
                    "api_execution_time",
                    "decision_to_creation_ms",
                    "creation_to_pre_api_ms", 
                    "pre_api_to_execution_ms",
                    "decision_to_pre_api_ms",
                    "decision_to_execution_ms",
                    "total_latency_ms"
                ])
    
    def record_decision_time(self, order_id: str) -> None:
        """
        Record the time when the decision to place an order was made in XEMM.
        
        :param order_id: The client order ID to track
        """
        self._decision_timestamps[order_id] = time.perf_counter()
    
    def start_tracking_order(self, order_id: str) -> None:
        """
        Start tracking an order by recording its creation timestamp.
        
        :param order_id: The client order ID to track
        """
        self._order_creation_timestamps[order_id] = time.perf_counter()
    
    def record_pre_api_time(self, order_id: str) -> None:
        """
        Record the time just before the API call is made.
        
        :param order_id: The client order ID to track
        """
        self._pre_api_timestamps[order_id] = time.perf_counter()
    
    def stop_tracking_order(
        self, 
        order_id: str, 
        exchange: str,
        trading_pair: str,
        side: str,
        order_type: str,
        price: Decimal,
        amount: Decimal
    ) -> None:
        """
        Stop tracking an order and record the execution time.
        
        :param order_id: The client order ID that was tracked
        :param exchange: The exchange where the order was placed
        :param trading_pair: The trading pair for the order
        :param side: The side of the order (BUY/SELL)
        :param order_type: The type of the order (LIMIT/MARKET)
        :param price: The price of the order
        :param amount: The amount of the order
        """
        if order_id in self._order_creation_timestamps:
            creation_time = self._order_creation_timestamps.pop(order_id)
            execution_time = time.perf_counter()
            
            # Get timestamps
            pre_api_time = self._pre_api_timestamps.pop(order_id, None)
            decision_time = self._decision_timestamps.pop(order_id, None)
            
            # Calculate latencies in milliseconds
            creation_to_pre_api_ms = None
            pre_api_to_execution_ms = None
            decision_to_creation_ms = None
            decision_to_pre_api_ms = None
            decision_to_execution_ms = None
            total_latency_ms = None
            
            if pre_api_time is not None:
                creation_to_pre_api_ms = (pre_api_time - creation_time) * 1000
                pre_api_to_execution_ms = (execution_time - pre_api_time) * 1000
            
            if decision_time is not None:
                decision_to_creation_ms = (creation_time - decision_time) * 1000
                decision_to_execution_ms = (execution_time - decision_time) * 1000
                total_latency_ms = decision_to_execution_ms
                if pre_api_time is not None:
                    decision_to_pre_api_ms = (pre_api_time - decision_time) * 1000
            
            # Write to CSV asynchronously
            safe_ensure_future(self._write_to_csv(
                order_id=order_id,
                exchange=exchange,
                trading_pair=trading_pair,
                side=side,
                order_type=order_type,
                price=price,
                amount=amount,
                decision_time=decision_time,
                creation_time=creation_time,
                pre_api_time=pre_api_time,
                execution_time=execution_time,
                decision_to_creation_ms=decision_to_creation_ms,
                creation_to_pre_api_ms=creation_to_pre_api_ms,
                pre_api_to_execution_ms=pre_api_to_execution_ms,
                decision_to_pre_api_ms=decision_to_pre_api_ms,
                decision_to_execution_ms=decision_to_execution_ms,
                total_latency_ms=total_latency_ms
            ))
