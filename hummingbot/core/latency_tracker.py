# file: latency_tracker.py
import csv
import os
import time
from as<PERSON><PERSON> import Lock
from dataclasses import dataclass, asdict
from typing import Optional, Dict


@dataclass
class LatencyRecord:
    """Store only timestamps for an order"""
    order_id: str
    order_id_generation_time: float
    pre_api_time: Optional[float] = None
    api_execution_time: Optional[float] = None
    confirmation_time: Optional[float] = None

    @property
    def order_id_to_pre_api_ms(self) -> Optional[float]:
        if self.pre_api_time is None:
            return None
        return (self.pre_api_time - self.order_id_generation_time) * 1000

    @property
    def pre_api_to_execution_ms(self) -> Optional[float]:
        if self.pre_api_time is None or self.api_execution_time is None:
            return None
        return (self.api_execution_time - self.pre_api_time) * 1000

    @property
    def api_execution_to_confirmation_ms(self) -> Optional[float]:
        if self.api_execution_time is None or self.confirmation_time is None:
            return None
        return (self.confirmation_time - self.api_execution_time) * 1000

    @property
    def total_latency_ms(self) -> Optional[float]:
        if self.confirmation_time is None:
            return None
        return (self.confirmation_time - self.order_id_generation_time) * 1000


class LatencyTracker:
    # … keep singleton boilerplate …

    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        self._records: Dict[str, LatencyRecord] = {}
        self._file_lock = Lock()
        self._csv_file_path = "order_latency_tracking.csv"
        self._ensure_csv_headers()

    def _ensure_csv_headers(self):
        """Only timestamp columns + computed intervals"""
        if not os.path.exists(self._csv_file_path):
            with open(self._csv_file_path, 'w', newline='') as csvfile:
                fieldnames = [
                    'order_id',
                    'order_id_generation_time',
                    'pre_api_time',
                    'api_execution_time',
                    'confirmation_time',
                    'order_id_to_pre_api_ms',
                    'pre_api_to_execution_ms',
                    'api_execution_to_confirmation_ms',
                    'total_latency_ms'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

    def start_tracking_order(self, order_id: str) -> None:
        """Only record the moment you got the order ID"""
        now = time.time()
        with self._lock:
            self._records[order_id] = LatencyRecord(
                order_id=order_id,
                order_id_generation_time=now
            )

    # mark_pre_api, mark_api_execution, mark_confirmation unchanged

    def _save_record_to_csv(self, record: LatencyRecord) -> None:
        """Save just the timestamps + ms intervals"""
        with self._file_lock:
            with open(self._csv_file_path, 'a', newline='') as csvfile:
                fieldnames = [
                    'order_id',
                    'order_id_generation_time',
                    'pre_api_time',
                    'api_execution_time',
                    'confirmation_time',
                    'order_id_to_pre_api_ms',
                    'pre_api_to_execution_ms',
                    'api_execution_to_confirmation_ms',
                    'total_latency_ms'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                row = {
                    'order_id': record.order_id,
                    'order_id_generation_time': record.order_id_generation_time,
                    'pre_api_time': record.pre_api_time,
                    'api_execution_time': record.api_execution_time,
                    'confirmation_time': record.confirmation_time,
                    'order_id_to_pre_api_ms': record.order_id_to_pre_api_ms,
                    'pre_api_to_execution_ms': record.pre_api_to_execution_ms,
                    'api_execution_to_confirmation_ms': record.api_execution_to_confirmation_ms,
                    'total_latency_ms': record.total_latency_ms
                }
                writer.writerow(row)

latency_tracker = LatencyTracker()